/******************************************************************************
algorithm header
All rights reserved I-NAV 2023 2033
*******************************************************************************/
/******************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
******************************************************************************/
#ifndef DRV_ALGORITHM_H_INCLUDED
#define DRV_ALGORITHM_H_INCLUDED

#undef COMMON_EXT
#ifdef  __GOL_ALGORITHM_C__
    #define COMMON_EXT
#else
    #define COMMON_EXT extern
#endif

#ifdef __cplusplus
extern "C" {
#endif

#ifndef linux
#include "gd32f4xx.h"
#endif


typedef struct
{
    //float gps_second;	//周内秒
    unsigned char counter;
    double accelGrp[3];	//加速度X,Y,Z轴
    double gyroGrp[3];	//角速度X,Y,Z轴
    double roll;			//横滚角
    double pitch;		//俯仰角
    double azimuth;		//航向角
    double sensorTemp;	//传感器温度
} IMU_PARSE_DATA_TypeDef;


typedef struct
{
    double magGrp[3];	//磁场强度X,Y,Z轴
} MAGNET_DATA_TypeDef;



typedef struct
{
    double gyroGrp[3];	//角速度X,Y,Z轴
    double sensorTemp;	//传感器温度
} IFOG_PARSE_DATA_TypeDef;

typedef struct _scha634_real_data
{

    double acc_x;
    double acc_y;
    double acc_z;
    double gyro_x;
    double gyro_y;
    double gyro_z;
    double temp_due;
    double temp_uno;

} scha634_real_data;

typedef struct 
{

    double acc_x;
    double acc_y;
    double acc_z;
    double gyro_x;
    double gyro_y;
    double gyro_z;
    double temp;

} adis16465_real_data,epson_real_data;

typedef struct
{
    float timestamp;					/* 时间戳, 单位: s , 精度: 0.0001*/
    unsigned char StarNum;					/* 星数 */
    unsigned char 		leapsec;
    unsigned char PositioningState;			/* 定位状态 */
    unsigned char ResolveState[3];			/* 解算状态 */
    unsigned char PositionType[3];			/* 位置/速度/姿态类型 */
    char LonHemisphere;					/* 经度半球 E东经 或 W西经  */
    double Lon;							/* 经度, 单位: °, 精度: 1e-7*/
    char LatHemisphere;					/* 纬度半球 N北纬 或 S南纬 */
    double Lat;							/* 纬度, 单位: °, 精度: 1e-7*/
    float Altitude;						/* 高度, 单位: m, 精度:0.01*/
    float Heading;						/* 航向角, 单位: °, 精度: 0.01*/
    float Pitch;						/* 俯仰角, 单位: °, 精度: 0.01*/
    float Roll;							/* 横滚角, 单位: °, 精度: 0.01*/
    float baseline;						/* 基线长, 单位: m*/
	float		Age;
	float 		trackTrue;
    float HDOP;							/* HDOP水平精度因子 0.5 - 99.9 */
    float GroundSpeed;					/* 速度, 单位: m/s, 精度: 0.1*/
    unsigned int	gpsweek;
    //unsigned int 		gpssecond; 
    unsigned long 		gpssecond; 
    float			ve;
    float 		vn;
    float			vu;
    unsigned int	rtkStatus;			//kinematic状态
    unsigned int	headingStatus;		//moving base状态
    unsigned char supportposvelstd;

    float LonStd;						/* 经度标准差, 单位: °*/
    float LatStd;						/* 纬度标准差, 单位: °*/
    float AltitudeStd;					/* 高度标准差, 单位: m*/

    float hdgstddev;					/* 航向角标准差, 单位： m*/
    float ptchstddev;					/* 俯仰角标准差, 单位: m */

    float		vestd;
    float		vnstd;
    float		vustd;

    unsigned short	ppsDelay;				//ms毫秒
	unsigned char	FpgaGnssUpdate;
	
	unsigned int gpssecond982;
	unsigned int velStatus;
} GNSSDataTypeDef;

#ifndef linux
#pragma pack(1)
#endif
typedef struct
{
    float gnssArmLength[3];
    float gnssAtt_from_vehicle[3];//双天线相对IMU的安装角度
    float OBArmLength[3];
    float OBAtt_from_vehicle[3];//车底盘相对IMU的安装角度
    unsigned char flag;				//bit0:gps杆臂是否被设置
    							//bit1:双天线相对IMU的安装角度是否被设置
    							//bit2:导航系统安装角是否被设置
    							//bit3:车底盘相对IMU的安装角度是否被设置
    							//bit4:双天线基线长度是否合法 
   float wb_set[3];				//degree/h
   unsigned char methodType;	//0:全程kalmnan , 1:直接轮速航位推算
   unsigned char sim; 				//调试标志0:正常运行 1:调试
   unsigned int  lostepoch;			//仿真调试失锁历元
   unsigned int  HP;			//是否启动高精度计算,0:不启动；1：启动
   
} Param_t; //

typedef struct
{
	double gyro_off[3]; 			  //陀螺初始零偏rad/s
	double acc_off[3];				  //加表初始零偏m/s2
	double gnssAtt_from_vehicle2[3];  //天线安装角精准误差，单位度
	unsigned char Nav_Standard_flag;		  //0:未标定 1：标定中 2：标定完成,目前可以设置0或2
	double att_ods2_b_filte_2;		//IMU与载体的y轴夹角	
	double att_ods2_b_filte_2_x;		//IMU与载体的x轴夹角
	double  att_ods2b_filter_deg[3];  //****新增一个完备的数组***单位：deg***
}AdjPara_t;

#ifndef linux
#pragma pack()
#endif

typedef struct
{
    float timestamp;					/* 时间戳, 单位:s , 精度：0.0001*/
    float WheelSpeed_Front_Left;		/* 轮速 左前, 单位: m/s, 精度：待定*/    
    float WheelSpeed_Back_Left;			/* 轮速 左后, 单位: m/s, 精度：待定*/
    float WheelSpeed_Front_Right;		/* 轮速 右前, 单位: m/s, 精度：待定*/
    float WheelSpeed_Back_Right;		/* 轮速 右后, 单位: m/s, 精度：待定*/
    float WheelSteer;					/* 方向盘, 单位: °, 精度：待定*/
    float OdoPulse_1;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
    float OdoPulse_2;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
    unsigned char Gear;						/* 汽车档位 */

} CanData_t;

typedef struct
{
    CanData_t data;
    unsigned int counter;
	unsigned char flag;
} CanDataTypeDef;

typedef struct
{
	unsigned char flag;		//标定参数是否有效 0:无效；1：有效
	double	factor[9];		//补偿因子
	double	zeroOffset[3];	//零偏
}_calib_t;


typedef struct
{
    IMU_PARSE_DATA_TypeDef 	imuInfo;
    scha634_real_data		scha634Info;
    adis16465_real_data		adis16465Info;
    epson_real_data			epsonInfo;
    IFOG_PARSE_DATA_TypeDef ifogInfo;
    GNSSDataTypeDef			gnssInfo;
    CanDataTypeDef			canInfo;
    Param_t					Param;
    AdjPara_t				Adj;
    unsigned short			fusion: 3;		//0:gps 融合
    										//1:constraint of motion
    										//2:constraint of motion + wheel
    										//3:no gps
    										//4:revered
    unsigned short			debug: 1;		//0:normal 1:debug mode
    unsigned short			imuSelect: 1;	//0:mems 1:ifog
    unsigned short			outputType: 3;	//0: 1:
    unsigned short			memsType: 3;	//0: imu460 1:scha63x 2:adis16465 3:epsonG3xx
    unsigned short			reserved: 5;
	MAGNET_DATA_TypeDef		magInfo;
	_calib_t				gyroCalib;
	_calib_t				accCalib;
	
        unsigned int                        ppsDelay;                        //gnss输出的PPS上升沿跟接收到数据的延迟时间
        float                                        factor_G;                //几何精度因子
        float                                        factor_pos;                //位置精度因子
        float                                        factor_time;        //时间精度因子
        float                                        factor_V;                //垂直精度因子
        float                                        factor_H;                //水平精度因子
        float                                        factor_N;                //北向精度因子        
        float                                        factor_E;                //东向精度因子 
								//****安装轴向调整******
        char Axis[3];//******XYZ的组合****
        float Axis_sign[3];//******XYZ组合的符号****
        unsigned char Axis_flag;//******上位机是否设置了有效轴向****
} CombineDataTypeDef;

#ifndef linux
COMMON_EXT CombineDataTypeDef combineData;
#else
CombineDataTypeDef combineData;
#endif

#ifdef __cplusplus
}
#endif


#endif

